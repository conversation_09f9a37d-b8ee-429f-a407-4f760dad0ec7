# Giải pháp tính toán Volume USD cho Affiliate Transactions

## Vấn đề hiện tại

Tại bảng `affiliate_transactions`, cột `volume_usd` đang được lấy cùng giá trị với cột `quote_amount`, nguyên nhân là do NATS Stream không thể trả về `volume_usd` ở Subject: `"agency.affiliate.xbit_tx"`. 

**Vấn đề cụ thể:**
- `quote_amount` = số lượng SOL (ví dụ: 2.5 SOL)
- `volume_usd` = giá trị USD (ví dụ: $250 USD)
- Hiện tại: `volume_usd = quote_amount` (sai)
- Đ<PERSON>g phải là: `volume_usd = quote_amount * sol_price_usd`

## Giải pháp đã triển khai

### 1. Cải thiện logic tính toán Volume USD

**File:** `internal/service/affiliate/affiliate_service.go`

#### Thay đổi chính:

1. **Thêm method `calculateVolumeUSD`:**
```go
func (s *AffiliateService) calculateVolumeUSD(ctx context.Context, quoteAmount decimal.Decimal) (decimal.Decimal, error) {
    // Ưu tiên sử dụng giá từ memory (real-time)
    s.priceMutex.RLock()
    latestPrice := s.latestSolPrice
    s.priceMutex.RUnlock()

    if !latestPrice.IsZero() {
        return quoteAmount.Mul(latestPrice), nil
    }

    // Fallback: lấy giá từ database
    priceSnapshot, err := s.affiliateRepo.GetLatestSolPrice(ctx)
    if err != nil {
        return decimal.Zero, fmt.Errorf("failed to get SOL price from database: %w", err)
    }

    // Cập nhật memory với giá từ database
    s.priceMutex.Lock()
    s.latestSolPrice = priceSnapshot.Price
    s.latestSolPriceTime = priceSnapshot.Timestamp
    s.priceMutex.Unlock()

    return quoteAmount.Mul(priceSnapshot.Price), nil
}
```

2. **Cập nhật `createNewTransaction`:**
```go
// Thay thế:
volumeUSD := txEvent.QuoteAmount.Mul(s.latestSolPrice)

// Bằng:
volumeUSD, err := s.calculateVolumeUSD(ctx, txEvent.QuoteAmount)
if err != nil {
    // Fallback: sử dụng quote_amount (backward compatibility)
    volumeUSD = txEvent.QuoteAmount
}
```

3. **Cập nhật `updateExistingTransaction`:**
```go
// Thay thế:
existingTx.VolumeUSD = txEvent.QuoteAmount

// Bằng:
volumeUSD, err := s.calculateVolumeUSD(ctx, txEvent.QuoteAmount)
if err != nil {
    volumeUSD = txEvent.QuoteAmount // Fallback
}
existingTx.VolumeUSD = volumeUSD
```

4. **Thêm khởi tạo SOL price từ database:**
```go
func (s *AffiliateService) initializeSolPriceFromDatabase() {
    ctx := context.Background()
    priceSnapshot, err := s.affiliateRepo.GetLatestSolPrice(ctx)
    if err != nil {
        global.GVA_LOG.Warn("Failed to initialize SOL price from database on startup", zap.Error(err))
        return
    }

    s.priceMutex.Lock()
    s.latestSolPrice = priceSnapshot.Price
    s.latestSolPriceTime = priceSnapshot.Timestamp
    s.priceMutex.Unlock()
}
```

### 2. Luồng hoạt động

#### Khi service khởi động:
1. `NewAffiliateService()` được gọi
2. `initializeSolPriceFromDatabase()` lấy SOL price mới nhất từ database
3. Lưu vào memory (`latestSolPrice`, `latestSolPriceTime`)

#### Khi nhận SOL price update từ NATS:
1. Subject: `"agency.affiliate.price.501424.So11111111111111111111111111111111111111112"`
2. `ProcessSolPriceUpdate()` cập nhật giá mới vào memory
3. Giá được lưu vào database khi có affiliate transaction

#### Khi nhận affiliate transaction từ NATS:
1. Subject: `"agency.affiliate.xbit_tx"`
2. `calculateVolumeUSD()` được gọi:
   - **Ưu tiên 1:** Sử dụng giá từ memory (real-time)
   - **Ưu tiên 2:** Lấy giá từ database (fallback)
   - **Ưu tiên 3:** Sử dụng quote_amount (backward compatibility)
3. `volume_usd = quote_amount * sol_price_usd`

### 3. Ưu điểm của giải pháp

1. **Tính toán chính xác:** `volume_usd` thực sự phản ánh giá trị USD
2. **Real-time:** Sử dụng giá SOL mới nhất từ NATS
3. **Fallback mechanism:** Có cơ chế dự phòng khi không có giá
4. **Backward compatibility:** Không làm hỏng dữ liệu cũ
5. **Thread-safe:** Sử dụng mutex để bảo vệ concurrent access
6. **Khởi tạo tự động:** Lấy giá từ database khi service start

### 4. Cấu trúc dữ liệu

#### NATS Subjects:
- **Transaction:** `"agency.affiliate.xbit_tx"`
  - `quote_amount`: Số lượng SOL
  - `quote_symbol`: "SOL"
  
- **SOL Price:** `"agency.affiliate.price.501424.So11111111111111111111111111111111111111112"`
  - `usd_price`: Giá SOL theo USD
  - `timestamp`: Thời gian cập nhật

#### Database Tables:
- **affiliate_transactions:**
  - `quote_amount`: decimal (SOL amount)
  - `volume_usd`: decimal (USD value) = quote_amount * sol_price
  
- **sol_price_snapshots:**
  - `price`: decimal (USD per SOL)
  - `timestamp`: timestamp (unique index)

### 5. Ví dụ tính toán

```
Input:
- quote_amount = 2.5 SOL
- sol_price_usd = 145.50 USD/SOL

Calculation:
- volume_usd = 2.5 * 145.50 = 363.75 USD

Result:
- affiliate_transactions.quote_amount = 2.5
- affiliate_transactions.volume_usd = 363.75
```

### 6. Monitoring và Logging

Giải pháp bao gồm logging chi tiết để theo dõi:
- Khởi tạo SOL price từ database
- Cập nhật SOL price từ NATS
- Tính toán volume USD
- Fallback scenarios
- Error handling

## Kết luận

Giải pháp này đảm bảo `volume_usd` được tính toán chính xác dựa trên giá SOL thực tế, đồng thời duy trì tính ổn định và khả năng tương thích ngược của hệ thống.
