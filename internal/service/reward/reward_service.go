package reward

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"go.uber.org/zap"
)

type RewardDataService struct {
	userRepo       repo.InvitationRepo
	commissionRepo transaction.CommissionLedgerRepositoryInterface
	affiliateRepo  transaction.AffiliateTransactionRepositoryInterface
	levelRepo      repo.LevelRepo
}

func NewRewardDataService() *RewardDataService {
	return &RewardDataService{
		userRepo:       &agent_referral.InvitationRepository{},
		commissionRepo: transaction.NewCommissionLedgerRepository(),
		affiliateRepo:  transaction.NewAffiliateTransactionRepository(),
		levelRepo:      repo.NewLevelRepository(),
	}
}

// WithdrawalRecord represents a withdrawal record with type information
type WithdrawalRecord struct {
	Hash             string `json:"hash"`
	WithdrawalReward string `json:"withdrawal_reward"`
	Date             string `json:"date"`
	Type             string `json:"type"` // "contract" or "meme"
}

// WithdrawalRecordsResult represents the result of withdrawal records query
type WithdrawalRecordsResult struct {
	Records []WithdrawalRecord `json:"records"`
	Total   int64              `json:"total"`
}

// GetWithdrawalRecords retrieves withdrawal records for a user with pagination
func (s *RewardDataService) GetWithdrawalRecords(ctx context.Context, userID uuid.UUID, page, pageSize int) (*WithdrawalRecordsResult, error) {
	var allRecords []WithdrawalRecord
	var totalCount int64

	// Calculate offset
	offset := (page - 1) * pageSize

	// Get contract withdrawal records (CommissionLedger with CLAIMED status)
	contractRecords, contractTotal, err := s.getContractWithdrawalRecords(ctx, userID, pageSize, offset)
	if err != nil {
		global.GVA_LOG.Error("Failed to get contract withdrawal records", zap.Error(err))
		return nil, fmt.Errorf("failed to get contract withdrawal records: %w", err)
	}

	// Get meme withdrawal records (MemeCommissionLedger with CLAIMED status)
	memeRecords, memeTotal, err := s.getMemeWithdrawalRecords(ctx, userID, pageSize, offset)
	if err != nil {
		global.GVA_LOG.Error("Failed to get meme withdrawal records", zap.Error(err))
		return nil, fmt.Errorf("failed to get meme withdrawal records: %w", err)
	}

	// Combine all records
	allRecords = append(allRecords, contractRecords...)
	allRecords = append(allRecords, memeRecords...)

	// Calculate total
	totalCount = contractTotal + memeTotal

	return &WithdrawalRecordsResult{
		Records: allRecords,
		Total:   totalCount,
	}, nil
}

// getContractWithdrawalRecords retrieves contract withdrawal records from CommissionLedger
func (s *RewardDataService) getContractWithdrawalRecords(ctx context.Context, userID uuid.UUID, pageSize, offset int) ([]WithdrawalRecord, int64, error) {
	var records []WithdrawalRecord
	var total int64

	// Query contract withdrawal records with transaction hash
	var result []struct {
		ID                  uuid.UUID       `json:"id"`
		CommissionAmount    decimal.Decimal `json:"commission_amount"`
		ClaimedAt           *time.Time      `json:"claimed_at"`
		TransactionHash     *string         `json:"transaction_hash"`
		SourceTransactionID string          `json:"source_transaction_id"`
	}

	// Get total count
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count contract withdrawal records: %w", err)
	}

	// Get paginated data with transaction hash
	err = global.GVA_DB.WithContext(ctx).Debug().
		Table("commission_ledger cl").
		Select(`
			cl.id,
			cl.commission_amount,
			cl.claimed_at,
			hlt.hash as transaction_hash,
			cl.source_transaction_id
		`).
		Joins("LEFT JOIN hyper_liquid_transactions hlt ON hlt.user_id = cl.recipient_user_id").
		Where("cl.recipient_user_id = ? AND cl.status = ?", userID, "CLAIMED").
		Order("cl.claimed_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get contract withdrawal records: %w", err)
	}

	// Convert to WithdrawalRecord format
	for _, item := range result {
		hash := ""
		if item.TransactionHash != nil && *item.TransactionHash != "" {
			hash = *item.TransactionHash
		}

		var date string
		if item.ClaimedAt != nil {
			date = item.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		records = append(records, WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: item.CommissionAmount.String(),
			Date:             date,
			Type:             "contract",
		})
	}

	return records, total, nil
}

// getMemeWithdrawalRecords retrieves meme withdrawal records from MemeCommissionLedger
func (s *RewardDataService) getMemeWithdrawalRecords(ctx context.Context, userID uuid.UUID, pageSize, offset int) ([]WithdrawalRecord, int64, error) {
	var records []WithdrawalRecord
	var total int64

	// Query meme withdrawal records with transaction hash
	var result []struct {
		ID                  uuid.UUID       `json:"id"`
		CommissionAmount    decimal.Decimal `json:"commission_amount"`
		CommissionAmountSol decimal.Decimal `json:"commission_amount_sol"`
		ClaimedAt           *time.Time      `json:"claimed_at"`
		TransactionHash     *string         `json:"transaction_hash"`
		SourceTransactionID string          `json:"source_transaction_id"`
	}

	// Get total count
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.MemeCommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count meme withdrawal records: %w", err)
	}

	// Get paginated data with transaction hash
	err = global.GVA_DB.WithContext(ctx).Debug().
		Table("meme_commission_ledger mcl").
		Select(`
			mcl.id,
			mcl.commission_amount,
			mcl.commission_amount_sol,
			mcl.claimed_at,
			at.tx_hash as transaction_hash,
			mcl.source_transaction_id
		`).
		Joins("LEFT JOIN affiliate_transactions at ON at.user_id = mcl.recipient_user_id").
		Where("mcl.recipient_user_id = ? AND mcl.status = ?", userID, "CLAIMED").
		Order("mcl.claimed_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get meme withdrawal records: %w", err)
	}

	// Convert to WithdrawalRecord format
	for _, item := range result {
		hash := ""
		if item.TransactionHash != nil && *item.TransactionHash != "" {
			hash = *item.TransactionHash
		}

		var date string
		if item.ClaimedAt != nil {
			date = item.ClaimedAt.Format("01-02")
		} else {
			date = "N/A"
		}

		// Use SOL amount if available, otherwise use USD amount
		withdrawalReward := item.CommissionAmountSol.String()
		if item.CommissionAmountSol.IsZero() {
			withdrawalReward = item.CommissionAmount.String()
		}

		records = append(records, WithdrawalRecord{
			Hash:             hash,
			WithdrawalReward: withdrawalReward,
			Date:             date,
			Type:             "meme",
		})
	}

	return records, total, nil
}

// GetInvitationRecords retrieves invitation records for a user with pagination
// 地址为user_id, 交易量为commission_ledger表合约的奖励，邀请奖励为meme_commission_ledger表meme的奖励
// 支持三级邀请关系：L1(直接), L2(间接), L3(延长)
func (s *RewardDataService) GetInvitationRecords(ctx context.Context, userID uuid.UUID, page, pageSize int) (*InvitationRecordsResult, error) {
	// Get all three-level invitation users (L1, L2, L3)
	threeLevelUserIDs, err := s.getThreeLevelInvitationUsers(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get three-level invitation users", zap.Error(err))
		return nil, fmt.Errorf("failed to get three-level invitation users: %w", err)
	}

	if len(threeLevelUserIDs) == 0 {
		return &InvitationRecordsResult{
			Records: []InvitationRecord{},
			Total:   0,
		}, nil
	}

	// For invitation records, we need to merge data from two different tables
	// To ensure accurate pagination, we'll fetch a reasonable amount of data and paginate in memory
	// This is necessary because we need to merge records by source_user_id across different tables

	// Set a reasonable limit to avoid fetching too much data
	// This limit should be large enough to handle most use cases but not so large as to cause memory issues
	maxFetchLimit := 10000

	// Get contract invitation records for three-level users
	contractRecords, _, err := s.getContractInvitationRecordsForUsers(ctx, threeLevelUserIDs, maxFetchLimit, 0)
	if err != nil {
		global.GVA_LOG.Error("Failed to get contract invitation records", zap.Error(err))
		return nil, fmt.Errorf("failed to get contract invitation records: %w", err)
	}

	// Get meme invitation records for three-level users
	memeRecords, _, err := s.getMemeInvitationRecordsForUsers(ctx, threeLevelUserIDs, maxFetchLimit, 0)
	if err != nil {
		global.GVA_LOG.Error("Failed to get meme invitation records", zap.Error(err))
		return nil, fmt.Errorf("failed to get meme invitation records: %w", err)
	}

	// Merge records by source_user_id
	mergedRecords := s.mergeInvitationRecords(contractRecords, memeRecords)

	// Calculate total unique source_user_ids after merging
	totalCount := int64(len(mergedRecords))

	// Apply pagination to merged results
	offset := (page - 1) * pageSize
	start := offset
	end := offset + pageSize

	var paginatedRecords []InvitationRecord
	if start >= len(mergedRecords) {
		paginatedRecords = []InvitationRecord{}
	} else {
		if end > len(mergedRecords) {
			end = len(mergedRecords)
		}
		paginatedRecords = mergedRecords[start:end]
	}

	return &InvitationRecordsResult{
		Records: paginatedRecords,
		Total:   totalCount,
	}, nil
}

// InvitationRecord represents an invitation record with commission information
type InvitationRecord struct {
	Address           string  `json:"address"`            // 被邀请用户的ID (source_user_id)
	TransactionVolume float64 `json:"transaction_volume"` // 合约奖励 (commission_ledger表的commission_amount)
	InvitedWithdrawal float64 `json:"invited_withdrawal"` // Meme奖励 (meme_commission_ledger表的commission_amount)
	Date              string  `json:"date"`               // 最新的创建日期 (两个表的created_at)
	ChainID           int     `json:"chain_id"`           // Chain ID (Arbitrum for contract, Solana for meme)
	Token             string  `json:"token"`              // Commission asset
}

// InvitationRecordsResult represents the result of invitation records query
type InvitationRecordsResult struct {
	Records []InvitationRecord `json:"records"`
	Total   int64              `json:"total"`
}

// getContractInvitationRecords retrieves contract invitation records from CommissionLedger
func (s *RewardDataService) getContractInvitationRecords(ctx context.Context, userID uuid.UUID, pageSize, offset int) ([]InvitationRecord, int64, error) {
	var records []InvitationRecord
	var total int64

	// Query contract invitation records grouped by source_user_id
	var result []struct {
		SourceUserID    uuid.UUID       `json:"source_user_id"`
		ContractReward  decimal.Decimal `json:"contract_reward"`
		CreatedAt       *time.Time      `json:"created_at"`
		CommissionAsset string          `json:"commission_asset"`
	}

	// Get total count - count unique source_user_ids
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.CommissionLedger{}).
		Select("COUNT(DISTINCT source_user_id)").
		Where("recipient_user_id = ?", userID).
		Scan(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count contract invitation records: %w", err)
	}

	// Get paginated data grouped by source_user_id
	err = global.GVA_DB.WithContext(ctx).Debug().
		Table("commission_ledger").
		Select(`
			source_user_id,
			SUM(commission_amount) as contract_reward,
			MAX(created_at) as created_at,
			commission_asset
		`).
		Where("recipient_user_id = ?", userID).
		Group("source_user_id, commission_asset").
		Order("MAX(created_at) DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get contract invitation records: %w", err)
	}

	// Convert to InvitationRecord format
	for _, item := range result {
		var date string
		if item.CreatedAt != nil {
			date = item.CreatedAt.Format("2006-01-02")
		} else {
			date = "N/A"
		}

		record := InvitationRecord{
			Address:           item.SourceUserID.String(),
			TransactionVolume: item.ContractReward.InexactFloat64(),
			InvitedWithdrawal: 0, // Contract records don't have meme rewards
			Date:              date,
			ChainID:           42161, // Arbitrum One chain ID
			Token:             item.CommissionAsset,
		}
		records = append(records, record)
	}

	return records, total, nil
}

// getMemeInvitationRecords retrieves meme invitation records from MemeCommissionLedger
func (s *RewardDataService) getMemeInvitationRecords(ctx context.Context, userID uuid.UUID, pageSize, offset int) ([]InvitationRecord, int64, error) {
	var records []InvitationRecord
	var total int64

	// Query meme invitation records grouped by source_user_id
	var result []struct {
		SourceUserID    uuid.UUID       `json:"source_user_id"`
		MemeReward      decimal.Decimal `json:"meme_reward"`
		CreatedAt       *time.Time      `json:"created_at"`
		CommissionAsset string          `json:"commission_asset"`
	}

	// Get total count - count unique source_user_ids
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.MemeCommissionLedger{}).
		Select("COUNT(DISTINCT source_user_id)").
		Where("recipient_user_id = ?", userID).
		Scan(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count meme invitation records: %w", err)
	}

	// Get paginated data grouped by source_user_id
	err = global.GVA_DB.WithContext(ctx).Debug().
		Table("meme_commission_ledger").
		Select(`
			source_user_id,
			SUM(commission_amount) as meme_reward,
			MAX(created_at) as created_at,
			commission_asset
		`).
		Where("recipient_user_id = ?", userID).
		Group("source_user_id, commission_asset").
		Order("MAX(created_at) DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get meme invitation records: %w", err)
	}

	// Convert to InvitationRecord format
	for _, item := range result {
		var date string
		if item.CreatedAt != nil {
			date = item.CreatedAt.Format("2006-01-02")
		} else {
			date = "N/A"
		}

		record := InvitationRecord{
			Address:           item.SourceUserID.String(),
			TransactionVolume: 0, // Meme records don't have contract rewards
			InvitedWithdrawal: item.MemeReward.InexactFloat64(),
			Date:              date,
			ChainID:           101, // Solana chain ID
			Token:             item.CommissionAsset,
		}
		records = append(records, record)
	}

	return records, total, nil
}

// mergeInvitationRecords merges contract and meme invitation records by source_user_id
func (s *RewardDataService) mergeInvitationRecords(contractRecords, memeRecords []InvitationRecord) []InvitationRecord {
	// Create a map to store merged records by address (source_user_id)
	mergedMap := make(map[string]*InvitationRecord)

	// Process contract records
	for _, record := range contractRecords {
		if existing, exists := mergedMap[record.Address]; exists {
			// Merge with existing record
			existing.TransactionVolume += record.TransactionVolume
			// Keep the latest date
			if record.Date > existing.Date {
				existing.Date = record.Date
			}
		} else {
			// Create new record
			newRecord := record
			mergedMap[record.Address] = &newRecord
		}
	}

	// Process meme records
	for _, record := range memeRecords {
		if existing, exists := mergedMap[record.Address]; exists {
			// Merge with existing record
			existing.InvitedWithdrawal += record.InvitedWithdrawal
			// Keep the latest date
			if record.Date > existing.Date {
				existing.Date = record.Date
			}
		} else {
			// Create new record
			newRecord := record
			mergedMap[record.Address] = &newRecord
		}
	}

	// Convert map back to slice and sort by date (latest first)
	var mergedRecords []InvitationRecord
	for _, record := range mergedMap {
		mergedRecords = append(mergedRecords, *record)
	}

	// Sort by date (latest first)
	for i := 0; i < len(mergedRecords)-1; i++ {
		for j := i + 1; j < len(mergedRecords); j++ {
			if mergedRecords[i].Date < mergedRecords[j].Date {
				mergedRecords[i], mergedRecords[j] = mergedRecords[j], mergedRecords[i]
			}
		}
	}

	return mergedRecords
}

// getThreeLevelInvitationUsers gets all three-level invitation users (L1, L2, L3) for a given user
func (s *RewardDataService) getThreeLevelInvitationUsers(ctx context.Context, userID uuid.UUID) ([]uuid.UUID, error) {
	var userIDs []uuid.UUID

	// Get L1 users (direct referrals)
	var l1Users []uuid.UUID
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.Referral{}).
		Where("referrer_id = ? AND depth = 1", userID).
		Pluck("user_id", &l1Users).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get L1 users: %w", err)
	}
	userIDs = append(userIDs, l1Users...)

	// Get L2 users (indirect referrals)
	var l2Users []uuid.UUID
	if len(l1Users) > 0 {
		err = global.GVA_DB.WithContext(ctx).
			Model(&model.Referral{}).
			Where("referrer_id IN ? AND depth = 1", l1Users).
			Pluck("user_id", &l2Users).Error
		if err != nil {
			return nil, fmt.Errorf("failed to get L2 users: %w", err)
		}
		userIDs = append(userIDs, l2Users...)
	}

	// Get L3 users (extended referrals)
	var l3Users []uuid.UUID
	if len(l2Users) > 0 {
		err = global.GVA_DB.WithContext(ctx).
			Model(&model.Referral{}).
			Where("referrer_id IN ? AND depth = 1", l2Users).
			Pluck("user_id", &l3Users).Error
		if err != nil {
			return nil, fmt.Errorf("failed to get L3 users: %w", err)
		}
		userIDs = append(userIDs, l3Users...)
	}

	// Remove duplicates
	uniqueUserIDs := make([]uuid.UUID, 0)
	seen := make(map[uuid.UUID]bool)
	for _, id := range userIDs {
		if !seen[id] {
			seen[id] = true
			uniqueUserIDs = append(uniqueUserIDs, id)
		}
	}

	return uniqueUserIDs, nil
}

// getContractInvitationRecordsForUsers retrieves contract invitation records for specific users
func (s *RewardDataService) getContractInvitationRecordsForUsers(ctx context.Context, userIDs []uuid.UUID, pageSize, offset int) ([]InvitationRecord, int64, error) {
	var records []InvitationRecord
	var total int64

	if len(userIDs) == 0 {
		return records, 0, nil
	}

	// Query contract invitation records grouped by source_user_id for the specified users
	var result []struct {
		SourceUserID    uuid.UUID       `json:"source_user_id"`
		ContractReward  decimal.Decimal `json:"contract_reward"`
		CreatedAt       *time.Time      `json:"created_at"`
		CommissionAsset string          `json:"commission_asset"`
	}

	// Get total count - count unique source_user_ids
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.CommissionLedger{}).
		Select("COUNT(DISTINCT source_user_id)").
		Where("source_user_id IN ?", userIDs).
		Scan(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count contract invitation records: %w", err)
	}

	// Get paginated data grouped by source_user_id
	err = global.GVA_DB.WithContext(ctx).Debug().
		Table("commission_ledger").
		Select(`
			source_user_id,
			SUM(commission_amount) as contract_reward,
			MAX(created_at) as created_at,
			commission_asset
		`).
		Where("source_user_id IN ?", userIDs).
		Group("source_user_id, commission_asset").
		Order("MAX(created_at) DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get contract invitation records: %w", err)
	}

	// Convert to InvitationRecord format
	for _, item := range result {
		var date string
		if item.CreatedAt != nil {
			date = item.CreatedAt.Format("2006-01-02")
		} else {
			date = "N/A"
		}

		record := InvitationRecord{
			Address:           item.SourceUserID.String(),
			TransactionVolume: item.ContractReward.InexactFloat64(),
			InvitedWithdrawal: 0, // Contract records don't have meme rewards
			Date:              date,
			ChainID:           42161, // Arbitrum One chain ID
			Token:             item.CommissionAsset,
		}
		records = append(records, record)
	}

	return records, total, nil
}

// getMemeInvitationRecordsForUsers retrieves meme invitation records for specific users
func (s *RewardDataService) getMemeInvitationRecordsForUsers(ctx context.Context, userIDs []uuid.UUID, pageSize, offset int) ([]InvitationRecord, int64, error) {
	var records []InvitationRecord
	var total int64

	if len(userIDs) == 0 {
		return records, 0, nil
	}

	// Query meme invitation records grouped by source_user_id for the specified users
	var result []struct {
		SourceUserID    uuid.UUID       `json:"source_user_id"`
		MemeReward      decimal.Decimal `json:"meme_reward"`
		CreatedAt       *time.Time      `json:"created_at"`
		CommissionAsset string          `json:"commission_asset"`
	}

	// Get total count - count unique source_user_ids
	err := global.GVA_DB.WithContext(ctx).Debug().
		Model(&model.MemeCommissionLedger{}).
		Select("COUNT(DISTINCT source_user_id)").
		Where("source_user_id IN ?", userIDs).
		Scan(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count meme invitation records: %w", err)
	}

	// Get paginated data grouped by source_user_id
	err = global.GVA_DB.WithContext(ctx).Debug().
		Table("meme_commission_ledger").
		Select(`
			source_user_id,
			SUM(commission_amount) as meme_reward,
			MAX(created_at) as created_at,
			commission_asset
		`).
		Where("source_user_id IN ?", userIDs).
		Group("source_user_id, commission_asset").
		Order("MAX(created_at) DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&result).Error

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get meme invitation records: %w", err)
	}

	// Convert to InvitationRecord format
	for _, item := range result {
		var date string
		if item.CreatedAt != nil {
			date = item.CreatedAt.Format("2006-01-02")
		} else {
			date = "N/A"
		}

		record := InvitationRecord{
			Address:           item.SourceUserID.String(),
			TransactionVolume: 0, // Meme records don't have contract rewards
			InvitedWithdrawal: item.MemeReward.InexactFloat64(),
			Date:              date,
			ChainID:           101, // Solana chain ID
			Token:             item.CommissionAsset,
		}
		records = append(records, record)
	}

	return records, total, nil
}
