package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
)

// TaskStatus represents the status of a user's task progress
type TaskStatus string

const (
	TaskStatusNotStarted TaskStatus = "NOT_STARTED"
	TaskStatusInProgress TaskStatus = "IN_PROGRESS"
	TaskStatusCompleted  TaskStatus = "COMPLETED"
	TaskStatusClaimed    TaskStatus = "CLAIMED"
	TaskStatusExpired    TaskStatus = "EXPIRED"
)

// TaskMetadata represents additional metadata for task progress
type TaskMetadata struct {
	LastTradingVolume   *float64               `json:"last_trading_volume,omitempty"`
	ConsecutiveStreak   *int                   `json:"consecutive_streak,omitempty"`
	SocialMediaData     map[string]interface{} `json:"social_media_data,omitempty"`
	VerificationDetails map[string]interface{} `json:"verification_details,omitempty"`
	CustomData          map[string]interface{} `json:"custom_data,omitempty"`
}

// <PERSON><PERSON> implements the sql.Scanner interface for TaskMetadata
func (tm *TaskMetadata) Scan(value interface{}) error {
	if value == nil {
		*tm = TaskMetadata{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, tm)
}

// Value implements the driver.Valuer interface for TaskMetadata
func (tm TaskMetadata) Value() (driver.Value, error) {
	return json.Marshal(tm)
}

// UserTaskProgress represents the user_task_progress table
type UserTaskProgress struct {
	ID              uuid.UUID     `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID          uuid.UUID     `gorm:"type:uuid;not null;index" json:"user_id"`
	TaskID          uuid.UUID     `gorm:"type:uuid;not null;index" json:"task_id"`
	Status          TaskStatus    `gorm:"type:varchar(20);not null;default:'NOT_STARTED'" json:"status"`
	ProgressValue   int           `gorm:"default:0" json:"progress_value"`
	TargetValue     *int          `json:"target_value"`
	CompletionCount int           `gorm:"default:0" json:"completion_count"`
	PointsEarned    int           `gorm:"default:0" json:"points_earned"`
	LastCompletedAt *time.Time    `json:"last_completed_at"`
	LastResetAt     *time.Time    `json:"last_reset_at"`
	StreakCount     int           `gorm:"default:0" json:"streak_count"`
	Metadata        *TaskMetadata `gorm:"type:jsonb" json:"metadata"`
	CreatedAt       time.Time     `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt       time.Time     `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
}

// TableName specifies the table name for UserTaskProgress
func (UserTaskProgress) TableName() string {
	return "user_task_progress"
}

// IsCompleted checks if the task is completed
func (utp *UserTaskProgress) IsCompleted() bool {
	return utp.Status == TaskStatusCompleted || utp.Status == TaskStatusClaimed
}

// CanBeClaimed checks if the task can be claimed
// Note: With auto-claim, tasks are automatically claimed when completed
func (utp *UserTaskProgress) CanBeClaimed() bool {
	return utp.Status == TaskStatusCompleted
}

// IsExpired checks if the task progress has expired
func (utp *UserTaskProgress) IsExpired() bool {
	return utp.Status == TaskStatusExpired
}

// GetProgressPercentage calculates the progress percentage
func (utp *UserTaskProgress) GetProgressPercentage() float64 {
	if utp.TargetValue == nil || *utp.TargetValue == 0 {
		return 0
	}

	percentage := float64(utp.ProgressValue) / float64(*utp.TargetValue) * 100
	if percentage > 100 {
		return 100
	}
	return percentage
}

// ShouldReset checks if the task should be reset based on the reset period
func (utp *UserTaskProgress) ShouldReset(resetPeriod ResetPeriod) bool {
	if utp.LastResetAt == nil {
		return true
	}

	now := time.Now()
	switch resetPeriod {
	case ResetDaily:
		return now.Sub(*utp.LastResetAt) >= 24*time.Hour
	case ResetWeekly:
		return now.Sub(*utp.LastResetAt) >= 7*24*time.Hour
	case ResetMonthly:
		// Check if we're in a different month
		return now.Month() != utp.LastResetAt.Month() || now.Year() != utp.LastResetAt.Year()
	case ResetNever:
		return false
	default:
		return false
	}
}

// Reset resets the task progress
func (utp *UserTaskProgress) Reset() {
	now := time.Now()
	utp.Status = TaskStatusNotStarted
	utp.ProgressValue = 0
	utp.CompletionCount = 0
	utp.LastResetAt = &now
	utp.UpdatedAt = now

	// Reset streak for non-consecutive tasks
	// Note: All task types can have streaks now, so we don't reset automatically
	// Streak logic will be handled by specific task processors
}

// ResetProgressive resets progressive task progress but preserves streak for consecutive tasks
func (utp *UserTaskProgress) ResetProgressive() {
	now := time.Now()
	utp.Status = TaskStatusNotStarted
	utp.ProgressValue = 0
	// Don't reset CompletionCount for progressive tasks - they can be completed multiple times
	utp.LastResetAt = &now
	utp.UpdatedAt = now

	// For consecutive checkin tasks, preserve streak count
	// The streak will be managed by the task handler logic
}
