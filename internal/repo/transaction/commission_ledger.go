package transaction

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// CommissionLedgerRepositoryInterface defines the interface for commission ledger operations
type CommissionLedgerRepositoryInterface interface {
	GetClaimedAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	GetPendingClaimAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
	GetClaimedAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error)
	GetPendingClaimAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error)
	GetRebateAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error)
	GetClaimedAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error)
	GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error)
	GetPendingCommissionsByUserID(ctx context.Context, userID uuid.UUID) ([]model.CommissionLedger, error)
	GetTotalAccumulatedUSDByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error)
}

type CommissionLedgerRepository struct {
	db *gorm.DB
}

func NewCommissionLedgerRepository() CommissionLedgerRepositoryInterface {
	return &CommissionLedgerRepository{
		db: global.GVA_DB,
	}
}

// GetClaimedAmountByUserID gets the total claimed commission amount for a user
func (r *CommissionLedgerRepository) GetClaimedAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "CLAIMED").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get claimed amount: %w", err)
	}

	return result.TotalAmount, nil
}

// GetPendingClaimAmountByUserID gets the total pending claim commission amount for a user
func (r *CommissionLedgerRepository) GetPendingClaimAmountByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get pending claim amount: %w", err)
	}

	return result.TotalAmount, nil
}

// GetClaimedAmountByUserIDAndType gets the total claimed commission amount for a user by transaction type
func (r *CommissionLedgerRepository) GetClaimedAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("source_transaction_type = ?", transactionType).
		Where("status = ?", "CLAIMED").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get claimed amount by type: %w", err)
	}

	return result.TotalAmount, nil
}

// GetPendingClaimAmountByUserIDAndType gets the total pending claim commission amount for a user by transaction type
func (r *CommissionLedgerRepository) GetPendingClaimAmountByUserIDAndType(ctx context.Context, userID uuid.UUID, transactionType string) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("source_transaction_type = ?", transactionType).
		Where("status = ?", "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get pending claim amount by type: %w", err)
	}

	return result.TotalAmount, nil
}

// GetRebateAmountByUserIDAndTypeAndPeriod gets the total rebate amount for a user by transaction type within a time period
func (r *CommissionLedgerRepository) GetRebateAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	query := r.db.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC)

	err := query.Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get rebate amount by type and period: %w", err)
	}

	return result.TotalAmount, nil
}

// GetClaimedAmountByUserIDAndTypeAndPeriod gets the total claimed commission amount for a user by transaction type within a time period
func (r *CommissionLedgerRepository) GetClaimedAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	query := r.db.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "CLAIMED").
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC)

	// If transaction type is not "ALL", filter by specific type
	if transactionType != "ALL" {
		query = query.Where("source_transaction_type = ?", transactionType)
	}

	err := query.Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get claimed amount by type and period: %w", err)
	}

	return result.TotalAmount, nil
}

// GetPendingClaimAmountByUserIDAndTypeAndPeriod gets the total pending claim commission amount for a user by transaction type within a time period
func (r *CommissionLedgerRepository) GetPendingClaimAmountByUserIDAndTypeAndPeriod(ctx context.Context, userID uuid.UUID, transactionType string, startTime, endTime time.Time) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	// Convert times to UTC and format for database query
	startTimeUTC := startTime.UTC()
	endTimeUTC := endTime.UTC()

	query := r.db.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "PENDING_CLAIM").
		Where("created_at::timestamp >= ?::timestamp AND created_at::timestamp < ?::timestamp", startTimeUTC, endTimeUTC)

	// If transaction type is not "ALL", filter by specific type
	if transactionType != "ALL" {
		query = query.Where("source_transaction_type = ?", transactionType)
	}

	err := query.Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get pending claim amount by type and period: %w", err)
	}

	return result.TotalAmount, nil
}

// GetPendingCommissionsByUserID gets all pending commissions for a user
func (r *CommissionLedgerRepository) GetPendingCommissionsByUserID(ctx context.Context, userID uuid.UUID) ([]model.CommissionLedger, error) {
	var commissions []model.CommissionLedger
	err := r.db.WithContext(ctx).
		Where("recipient_user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Find(&commissions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get pending commissions: %w", err)
	}

	return commissions, nil
}

// GetTotalAccumulatedUSDByUserID gets the total accumulated USD for a user
func (r *CommissionLedgerRepository) GetTotalAccumulatedUSDByUserID(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get total accumulated USD: %w", err)
	}

	return result.TotalAmount, nil
}
